/**
 * 3D Viewer Module for displaying OBJ files using Three.js
 */

import * as THREE from "three";
import { OBJLoader } from "three/addons/loaders/OBJLoader.js";
import { OrbitControls } from "three/addons/controls/OrbitControls.js";

class Viewer3D {
  constructor(containerId) {
    console.log(
      "🎯 Viewer3D constructor called with containerId:",
      containerId
    );
    this.container = document.getElementById(containerId);
    console.log("🎯 Container found:", !!this.container);
    if (this.container) {
      console.log("🎯 Container classes:", this.container.className);
      console.log(
        "🎯 Container size:",
        this.container.clientWidth,
        "x",
        this.container.clientHeight
      );
    }

    this.scene = null;
    this.camera = null;
    this.renderer = null;
    this.controls = null;
    this.currentModel = null;
    this.currentObjPath = null;
    this.isWireframe = false;

    this.loadingElement = document.getElementById("viewer-loading");
    this.errorElement = document.getElementById("viewer-error");

    this.init();
  }

  init() {
    console.log("🎯 init() called");
    if (!this.container) {
      console.error("❌ 3D Viewer container not found");
      return;
    }

    // Check if Three.js is loaded
    if (typeof THREE === "undefined") {
      console.error("❌ Three.js library not loaded");
      this.showError("3D viewer library not loaded");
      return;
    }
    console.log("✅ Three.js library loaded");

    // Create scene
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0x2a2a2a);
    console.log("✅ Scene created");

    // Create camera - use default aspect if container is hidden
    let width = this.container.clientWidth || 800;
    let height = this.container.clientHeight || 600;
    const aspect = width / height;
    console.log("🎯 Using size:", width, "x", height, "aspect:", aspect);
    this.camera = new THREE.PerspectiveCamera(75, aspect, 0.1, 1000);
    this.camera.position.set(5, 5, 5);
    console.log("✅ Camera created at position:", this.camera.position);

    // Create renderer
    this.renderer = new THREE.WebGLRenderer({ antialias: true });
    this.renderer.setSize(width, height);
    this.renderer.shadowMap.enabled = true;
    this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    console.log("✅ Renderer created with size:", width, "x", height);

    this.container.appendChild(this.renderer.domElement);
    console.log("✅ Renderer canvas appended to container");

    // Add lights
    this.setupLights();
    console.log("✅ Lights setup complete");

    // Add controls
    try {
      this.controls = new OrbitControls(this.camera, this.renderer.domElement);
      this.controls.enableDamping = true;
      this.controls.dampingFactor = 0.05;
      this.controls.enableZoom = true;
      this.controls.enablePan = true;
      console.log("✅ OrbitControls initialized");
    } catch (error) {
      console.warn(
        "⚠️ OrbitControls not loaded - mouse controls will be limited",
        error
      );
    }

    // Handle window resize
    window.addEventListener("resize", () => this.onWindowResize());

    // Add a test cube to verify renderer is working
    this.addTestCube();

    // Start render loop
    this.animate();
    console.log("✅ Animation loop started");
  }

  setupLights() {
    // Ambient light
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    this.scene.add(ambientLight);

    // Directional light
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 10, 5);
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    this.scene.add(directionalLight);

    // Point light
    const pointLight = new THREE.PointLight(0xffffff, 0.5);
    pointLight.position.set(-10, 10, -10);
    this.scene.add(pointLight);
  }

  addTestCube() {
    // Add a simple test cube to verify the renderer is working
    const geometry = new THREE.BoxGeometry(1, 1, 1);
    const material = new THREE.MeshPhongMaterial({ color: 0xff0000 });
    const cube = new THREE.Mesh(geometry, material);
    cube.position.set(0, 0, 0);
    this.scene.add(cube);
    console.log("🎯 Test cube added to scene");
  }

  showLoading() {
    if (this.loadingElement) {
      this.loadingElement.classList.remove("hidden");
    }
    if (this.errorElement) {
      this.errorElement.classList.add("hidden");
    }
  }

  hideLoading() {
    if (this.loadingElement) {
      this.loadingElement.classList.add("hidden");
    }
  }

  showError(message = "Failed to load 3D model") {
    if (this.errorElement) {
      this.errorElement.querySelector("p").textContent = message;
      this.errorElement.classList.remove("hidden");
    }
    this.hideLoading();
  }

  hideError() {
    if (this.errorElement) {
      this.errorElement.classList.add("hidden");
    }
  }

  showSuccessMessage() {
    // Create temporary success message
    const successDiv = document.createElement("div");
    successDiv.className = "viewer-success";
    successDiv.style.cssText = `
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(34, 197, 94, 0.9);
      color: white;
      padding: 15px 20px;
      border-radius: 8px;
      text-align: center;
      z-index: 60;
      animation: fadeInOut 2s ease-in-out;
    `;
    successDiv.innerHTML = `
      <i class="fas fa-check-circle text-xl mb-2"></i>
      <p>3D Model Loaded Successfully!</p>
    `;

    this.container.appendChild(successDiv);

    // Remove after animation
    setTimeout(() => {
      if (successDiv.parentNode) {
        successDiv.parentNode.removeChild(successDiv);
      }
    }, 2000);
  }

  loadOBJ(objPath) {
    console.log("🔄 loadOBJ called with path:", objPath);
    console.log(
      "🎯 Container visible:",
      !this.container.classList.contains("hidden")
    );
    console.log("🎯 Scene exists:", !!this.scene);
    console.log("🎯 Renderer exists:", !!this.renderer);

    this.showLoading();
    this.hideError();
    this.currentObjPath = objPath;

    // Remove existing model
    if (this.currentModel) {
      this.scene.remove(this.currentModel);
      this.currentModel = null;
    }

    // Load OBJ file
    const loader = new OBJLoader();

    loader.load(
      objPath,
      (object) => {
        console.log("✅ OBJ loaded successfully:", object);
        this.onModelLoaded(object);
      },
      (progress) => {
        console.log("📊 Loading progress:", progress);
      },
      (error) => {
        console.error("❌ Error loading OBJ:", error);
        this.showError("Failed to load 3D model");
      }
    );
  }

  onModelLoaded(object) {
    console.log("🎯 onModelLoaded called with object:", object);
    this.currentModel = object;

    // Apply default material with better appearance
    object.traverse((child) => {
      if (child.isMesh) {
        child.material = new THREE.MeshPhongMaterial({
          color: 0x4a90e2,
          shininess: 30,
          side: THREE.DoubleSide,
          transparent: false,
        });
        child.castShadow = true;
        child.receiveShadow = true;
      }
    });

    // Center and scale the model
    this.centerAndScaleModel(object);

    // Add to scene
    this.scene.add(object);
    console.log(
      "🎯 Object added to scene. Scene children count:",
      this.scene.children.length
    );

    // Reset camera position
    this.resetCamera();

    this.hideLoading();
    console.log("✅ 3D model loaded successfully");

    // Show success message
    this.showSuccessMessage();

    // Force render
    if (this.renderer && this.scene && this.camera) {
      this.renderer.render(this.scene, this.camera);
      console.log("🎯 Forced render after model load");
    }
  }

  centerAndScaleModel(object) {
    // Calculate bounding box
    const box = new THREE.Box3().setFromObject(object);
    const center = box.getCenter(new THREE.Vector3());
    const size = box.getSize(new THREE.Vector3());

    // Center the model
    object.position.sub(center);

    // Scale the model to fit in view
    const maxDim = Math.max(size.x, size.y, size.z);
    const scale = 3 / maxDim;
    object.scale.setScalar(scale);
  }

  resetCamera() {
    if (this.camera && this.controls) {
      this.camera.position.set(5, 5, 5);
      this.camera.lookAt(0, 0, 0);
      this.controls.reset();
    }
  }

  toggleWireframe() {
    if (!this.currentModel) return;

    this.isWireframe = !this.isWireframe;

    this.currentModel.traverse((child) => {
      if (child.isMesh) {
        if (this.isWireframe) {
          // Create wireframe material
          child.material = new THREE.MeshBasicMaterial({
            color: 0x00ff00,
            wireframe: true,
            side: THREE.DoubleSide,
          });
        } else {
          // Restore solid material
          child.material = new THREE.MeshPhongMaterial({
            color: 0x4a90e2,
            shininess: 30,
            side: THREE.DoubleSide,
            transparent: false,
          });
        }
      }
    });

    console.log(`Wireframe mode: ${this.isWireframe ? "ON" : "OFF"}`);
  }

  downloadModel() {
    if (this.currentObjPath) {
      const link = document.createElement("a");
      link.href = this.currentObjPath;
      link.download = this.currentObjPath.split("/").pop();
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }

  show() {
    console.log("🎯 show() called");
    if (this.container) {
      console.log("🎯 Container found, removing hidden class");
      this.container.classList.remove("hidden");
      console.log("🎯 Container classes after show:", this.container.className);

      // Hide default empty state
      const emptyState = document.getElementById("default-empty-state");
      if (emptyState) {
        emptyState.classList.add("hidden");
        console.log("🎯 Hidden default empty state");
      }
      this.onWindowResize();
      console.log("🎯 Window resize called");
    } else {
      console.error("❌ Container not found in show()");
    }
  }

  hide() {
    if (this.container) {
      this.container.classList.add("hidden");
      // Show default empty state
      const emptyState = document.getElementById("default-empty-state");
      if (emptyState) {
        emptyState.classList.remove("hidden");
      }
    }
  }

  onWindowResize() {
    if (!this.camera || !this.renderer || !this.container) return;

    const width = this.container.clientWidth;
    const height = this.container.clientHeight;

    console.log("🎯 onWindowResize - Container size:", width, "x", height);
    console.log(
      "🎯 Container offsetWidth/Height:",
      this.container.offsetWidth,
      "x",
      this.container.offsetHeight
    );

    this.camera.aspect = width / height;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(width, height);

    console.log("🎯 Renderer size set to:", width, "x", height);
  }

  animate() {
    requestAnimationFrame(() => this.animate());

    if (this.controls) {
      this.controls.update();
    }

    if (this.renderer && this.scene && this.camera) {
      this.renderer.render(this.scene, this.camera);
    }
  }

  dispose() {
    if (this.renderer) {
      this.renderer.dispose();
    }
    if (this.controls) {
      this.controls.dispose();
    }
  }
}

// Global viewer instance
let viewer3D = null;

// Initialize viewer when DOM is loaded
document.addEventListener("DOMContentLoaded", function () {
  console.log("🎯 DOM loaded, initializing 3D viewer");
  console.log("🎯 THREE available:", typeof THREE !== "undefined");
  console.log("🎯 OBJLoader available:", typeof OBJLoader !== "undefined");
  console.log(
    "🎯 OrbitControls available:",
    typeof OrbitControls !== "undefined"
  );

  viewer3D = new Viewer3D("viewer-3d-container");
  console.log("🎯 viewer3D initialized:", viewer3D);
});

// Global functions for controls
function resetCamera() {
  if (viewer3D) {
    viewer3D.resetCamera();
  }
}

function toggleWireframe() {
  if (viewer3D) {
    viewer3D.toggleWireframe();
  }
}

function downloadModel() {
  if (viewer3D) {
    viewer3D.downloadModel();
  }
}

// Function to load OBJ file (called from main.js)
function loadOBJModel(objPath) {
  console.log("🎯 loadOBJModel called with path:", objPath);
  if (viewer3D) {
    console.log("🎯 viewer3D exists, showing and loading OBJ");
    viewer3D.show();
    viewer3D.loadOBJ(objPath);
  } else {
    console.error("❌ viewer3D not initialized");
  }
}

// Function to hide 3D viewer
function hide3DViewer() {
  if (viewer3D) {
    viewer3D.hide();
  }
}

// Make functions available globally
window.loadOBJModel = loadOBJModel;
window.hide3DViewer = hide3DViewer;
window.resetCamera = resetCamera;
window.toggleWireframe = toggleWireframe;
window.downloadModel = downloadModel;
